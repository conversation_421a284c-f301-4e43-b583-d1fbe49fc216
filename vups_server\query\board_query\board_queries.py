"""
Optimized query functions for board data endpoints
Performance improvements:
1. Database-level set operations instead of Python set operations
2. Efficient SQL queries with proper indexing
3. Reduced memory usage by processing data in database
"""

import asyncpg
from collections import defaultdict
from functools import lru_cache
from typing import Dict, Any, Set, Tuple

from vups_server.sql.db_pool import get_connection
from vups.logger import logger


@lru_cache(maxsize=64)
def _cache_key_for_target_flow(target: str, current: str, previous: str, n: int) -> str:
    """Generate cache key for target flow analysis"""
    return f"target_flow_{target}_{current}_{previous}_{n}"


async def collect_target_flow_with_target_vtuber_optimized(
    target_vtube_name: str,
    current_time_str: str,
    previous_time_str: str,
    n: int = 5
):
    """
    OPTIMIZED: Use database-level operations for flow analysis
    Reduces memory usage and improves performance by doing set operations in SQL
    """
    try:
        async with get_connection() as conn:
            # OPTIMIZATION: Single query with database-level set operations
            flow_analysis_query = """
                WITH target_current AS (
                    SELECT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time::text = $2 AND up_name = $1
                ),
                target_previous AS (
                    SELECT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time::text = $3 AND up_name = $1
                ),
                all_current AS (
                    SELECT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time::text = $2 AND up_name != $1
                ),
                all_previous AS (
                    SELECT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time::text = $3 AND up_name != $1
                ),
                -- Users who flowed IN to target (in current but not in previous)
                flowed_in_users AS (
                    SELECT tc.uid, tc.username, tc.face
                    FROM target_current tc
                    LEFT JOIN target_previous tp ON tc.uid = tp.uid
                    WHERE tp.uid IS NULL
                ),
                -- Users who flowed OUT from target (in previous but not in current)
                flowed_out_users AS (
                    SELECT tp.uid, tp.username, tp.face
                    FROM target_previous tp
                    LEFT JOIN target_current tc ON tp.uid = tc.uid
                    WHERE tc.uid IS NULL
                ),
                -- Find where flowed_in users came from
                inflow_sources AS (
                    SELECT
                        ap.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap.uid::text, ap.username, ap.face]) as users
                    FROM flowed_in_users fiu
                    INNER JOIN all_previous ap ON fiu.uid = ap.uid
                    GROUP BY ap.up_name
                    ORDER BY user_count DESC
                    LIMIT $4
                ),
                -- Find where flowed_out users went to
                outflow_targets AS (
                    SELECT
                        ac.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ac.uid::text, ac.username, ac.face]) as users
                    FROM flowed_out_users fou
                    INNER JOIN all_current ac ON fou.uid = ac.uid
                    GROUP BY ac.up_name
                    ORDER BY user_count DESC
                    LIMIT $4
                )
                SELECT
                    'in' as flow_type, up_name, users
                FROM inflow_sources
                UNION ALL
                SELECT
                    'out' as flow_type, up_name, users
                FROM outflow_targets
            """

            flow_results = await conn.fetch(
                flow_analysis_query,
                target_vtube_name, current_time_str, previous_time_str, n
            )

            # Process results into expected format
            result = {'in': {}, 'out': {}}

            for row in flow_results:
                flow_type = row['flow_type']
                up_name = row['up_name']
                users = row['users']

                # Convert array format to expected tuple format
                user_tuples = []
                for user_array in users:
                    if len(user_array) >= 3:
                        user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

                result[flow_type][up_name] = user_tuples

            return result

    except asyncpg.PostgresError as e:
        logger.error(f"Database operation failed for target flow analysis: {e}")
        return None
    except Exception as e:
        logger.error(f"Error in optimized target flow analysis: {e}")
        return None


async def collect_target_flow_with_target_vtuber_by_period_optimized(
    target_vtube_name: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5
):
    """
    OPTIMIZED: Use database-level operations for period flow analysis
    Handles date ranges efficiently in SQL
    """
    try:
        # Convert date strings to datetime.date objects
        import datetime
        start_date1 = datetime.datetime.strptime(start_time_str1, '%Y-%m-%d').date()
        end_date1 = datetime.datetime.strptime(end_time_str1, '%Y-%m-%d').date()
        start_date2 = datetime.datetime.strptime(start_time_str2, '%Y-%m-%d').date()
        end_date2 = datetime.datetime.strptime(end_time_str2, '%Y-%m-%d').date()

        async with get_connection() as conn:
            # OPTIMIZATION: Single query with database-level period operations
            period_flow_query = """
                WITH target_period1 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name = $1
                ),
                target_period2 AS (
                    SELECT DISTINCT uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name = $1
                ),
                all_period1 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $2 AND $3
                    AND up_name != $1
                ),
                all_period2 AS (
                    SELECT DISTINCT up_name, uid, username, face
                    FROM dahanghai_list_table
                    WHERE time BETWEEN $4 AND $5
                    AND up_name != $1
                ),
                -- Users who flowed IN to target (in period2 but not in period1)
                flowed_in_users AS (
                    SELECT tp2.uid, tp2.username, tp2.face
                    FROM target_period2 tp2
                    LEFT JOIN target_period1 tp1 ON tp2.uid = tp1.uid
                    WHERE tp1.uid IS NULL
                ),
                -- Users who flowed OUT from target (in period1 but not in period2)
                flowed_out_users AS (
                    SELECT tp1.uid, tp1.username, tp1.face
                    FROM target_period1 tp1
                    LEFT JOIN target_period2 tp2 ON tp1.uid = tp2.uid
                    WHERE tp2.uid IS NULL
                ),
                -- Find where flowed_in users came from
                inflow_sources AS (
                    SELECT
                        ap1.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap1.uid::text, ap1.username, ap1.face]) as users
                    FROM flowed_in_users fiu
                    INNER JOIN all_period1 ap1 ON fiu.uid = ap1.uid
                    GROUP BY ap1.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                ),
                -- Find where flowed_out users went to
                outflow_targets AS (
                    SELECT
                        ap2.up_name,
                        COUNT(*) as user_count,
                        ARRAY_AGG(ARRAY[ap2.uid::text, ap2.username, ap2.face]) as users
                    FROM flowed_out_users fou
                    INNER JOIN all_period2 ap2 ON fou.uid = ap2.uid
                    GROUP BY ap2.up_name
                    ORDER BY user_count DESC
                    LIMIT $6
                )
                SELECT
                    'in' as flow_type, up_name, users
                FROM inflow_sources
                UNION ALL
                SELECT
                    'out' as flow_type, up_name, users
                FROM outflow_targets
            """

            flow_results = await conn.fetch(
                period_flow_query,
                target_vtube_name, start_date1, end_date1,
                start_date2, end_date2, n
            )

            # Process results into expected format
            result = {'in': {}, 'out': {}}

            for row in flow_results:
                flow_type = row['flow_type']
                up_name = row['up_name']
                users = row['users']

                # Convert array format to expected tuple format
                user_tuples = []
                for user_array in users:
                    if len(user_array) >= 3:
                        user_tuples.append((int(user_array[0]), user_array[1], user_array[2]))

                result[flow_type][up_name] = user_tuples

            return result

    except asyncpg.PostgresError as e:
        logger.error(f"Database operation failed for period flow analysis: {e}")
        return None
    except Exception as e:
        logger.error(f"Error in optimized period flow analysis: {e}")
        return None


# Additional helper functions for caching and performance monitoring

def clear_flow_analysis_cache():
    """Clear the LRU cache for flow analysis functions"""
    _cache_key_for_target_flow.cache_clear()


async def get_dahanghai_table_stats():
    """Get statistics about dahanghai_list_table for monitoring"""
    try:
        async with get_connection() as conn:
            stats_query = """
                SELECT
                    COUNT(*) as total_records,
                    COUNT(DISTINCT up_name) as unique_vtubers,
                    COUNT(DISTINCT uid) as unique_users,
                    MIN(time) as earliest_date,
                    MAX(time) as latest_date
                FROM dahanghai_list_table
            """

            stats = await conn.fetchrow(stats_query)
            return dict(stats) if stats else {}

    except Exception as e:
        logger.error(f"Error getting dahanghai table stats: {e}")
        return {}

"""
Optimized query functions for live info endpoints
Performance improvements:
1. Combined queries to reduce database round trips
2. Efficient JOINs instead of multiple separate queries
3. Optimized data processing
"""

import asyncio
import asyncpg
from datetime import datetime, timedelta
from decimal import Decimal
from functools import lru_cache
from typing import Optional, Dict, Any, List

from sql.db_pool import get_connection
from vups.logger import logger


@lru_cache(maxsize=128)
def _cache_key_for_room_sessions(room_id: str, limit: int = 50) -> str:
    """Generate cache key for room sessions"""
    return f"room_sessions_{room_id}_{limit}"


async def query_live_info_with_room_id_optimized(room_id: str, limit: int = 50):
    """
    OPTIMIZED: Single query with JOIN to get all session data at once
    Reduces database round trips from N+1 to 1 query
    """
    result = {
        "lastLiveDate": "", "lastLiveDanmakuCount": 0, "lastLiveIncome": 0.0,
        "lastAveEnterRoomCount": 0, "lastEnterRoomCount": 0, "lastAveOnlineRankCount": 0,
        "totalDanmakuCount": 0, "totalIncome": 0.0, "totalLiveCount": 0,
        "totalLiveSecond": 0.0, "sessions": [],
    }
    total_duration = timedelta()

    try:
        async with get_connection() as conn:
            # OPTIMIZATION: Single optimized query with all needed data
            optimized_query = """
                WITH session_stats AS (
                    SELECT
                        ls.live_id, ls.danmu_count, ls.start_time_str, ls.end_time_str,
                        ls.income, ls.watch_change_count, ls.like_info_update_count, ls.pay_count,
                        ls.interaction_count, ls.max_online_rank, ls.ave_online_rank, ls.ave_enter_room,
                        ls.enter_room_count, ls.datetime, ls.parent_area, ls.area, ls.cover, ls.title,
                        -- Get live status info efficiently
                        EXISTS(
                            SELECT 1 FROM live_status_minute_table lsm
                            WHERE lsm.live_id = ls.live_id AND lsm.live_action = '结束直播'
                        ) as is_finish,
                        EXISTS(
                            SELECT 1 FROM live_status_minute_table lsm
                            WHERE lsm.live_id = ls.live_id AND lsm.live_action = '开始直播'
                        ) as is_full
                    FROM live_session_table ls
                    WHERE ls.room_id = $1
                    ORDER BY ls.datetime DESC, ls.start_time_str DESC
                    LIMIT $2
                )
                SELECT * FROM session_stats
            """

            all_sessions = await conn.fetch(optimized_query, str(room_id), limit)

            if not all_sessions:
                logger.info(f"房间 {room_id} 没有找到直播会话记录。")
                return result

            result["totalLiveCount"] = len(all_sessions)
            result["totalDanmakuCount"] = sum(s["danmu_count"] for s in all_sessions if s["danmu_count"] is not None)
            result["totalIncome"] = float(sum(Decimal(str(s["income"])) for s in all_sessions if s["income"] is not None))

            # OPTIMIZATION: Process sessions data directly without additional queries
            sessions_list = []
            for session in all_sessions:
                # Calculate duration for stats
                if session["start_time_str"] and session["end_time_str"]:
                    try:
                        start_time = datetime.strptime(session["start_time_str"], "%Y-%m-%d %H:%M:%S")
                        end_time = datetime.strptime(session["end_time_str"], "%Y-%m-%d %H:%M:%S")
                        if end_time > start_time:
                            duration = end_time - start_time
                            total_duration += duration
                    except (ValueError, TypeError) as e:
                        logger.warning(f"解析时间字符串时出错 live_id={session['live_id']}: {e}")

                # Build session info directly from query results
                live_info = {
                    "liveId": session["live_id"],
                    "isFinish": session["is_finish"],
                    "isFull": session["is_full"],
                    "parentArea": session["parent_area"] or "",
                    "area": session["area"] or "",
                    "coverUrl": session["cover"] or "",
                    "danmakusCount": session["danmu_count"] or 0,
                    "startDate": session["start_time_str"] or "",
                    "stopDate": session["end_time_str"] or "",
                    "title": session["title"] or "",
                    "totalIncome": float(session["income"]) if session["income"] else 0.0,
                    "watchCount": session["watch_change_count"] or 0,
                    "likeCount": session["like_info_update_count"] or 0,
                    "payCount": session["pay_count"] or 0,
                    "interactionCount": session["interaction_count"] or 0,
                    "onlineRank": session["max_online_rank"] or 0,
                    "aveOnlineRank": session["ave_online_rank"] or 0,
                    "aveEnterRoom": session["ave_enter_room"] or 0,
                }
                sessions_list.append(live_info)

            result["sessions"] = sessions_list
            result["totalLiveSecond"] = total_duration.total_seconds()

            if all_sessions:
                latest_session = all_sessions[0]
                result.update({
                    "lastLiveDate": latest_session["datetime"].strftime("%Y-%m-%d") if latest_session["datetime"] else "",
                    "lastLiveDanmakuCount": latest_session["danmu_count"] or 0,
                    "lastLiveIncome": float(latest_session["income"]) if latest_session["income"] else 0.0,
                    "lastAveEnterRoomCount": latest_session["ave_enter_room"] or 0,
                    "lastEnterRoomCount": latest_session["enter_room_count"] or 0,
                    "lastAveOnlineRankCount": latest_session["ave_online_rank"] or 0,
                })

    except asyncpg.PostgresError as e:
        logger.error(f"查询房间 {room_id} 的数据库操作失败: {e}")
        result["sessions"] = []
    except Exception as e:
        logger.error(f"处理房间 {room_id} 数据时发生意外错误: {e}")
        result["sessions"] = []

    return result


async def query_whole_live_info_with_live_id_optimized(live_id: str):
    """
    OPTIMIZED: Single query with JOIN to get all live info at once
    Reduces database round trips from 3 to 1 query
    """
    try:
        async with get_connection() as conn:
            # OPTIMIZATION: Single comprehensive query
            optimized_query = """
                SELECT
                    ls.live_id, ls.danmu_count, ls.start_time_str, ls.end_time_str,
                    ls.parent_area, ls.area, ls.cover, ls.title, ls.income,
                    ls.watch_change_count, ls.like_info_update_count, ls.pay_count,
                    ls.interaction_count, ls.max_online_rank, ls.enter_room_count,
                    ls.ave_online_rank, ls.ave_enter_room,
                    -- Get live status info efficiently
                    EXISTS(
                        SELECT 1 FROM live_status_minute_table lsm
                        WHERE lsm.live_id = ls.live_id AND lsm.live_action = '结束直播'
                    ) as is_finish,
                    EXISTS(
                        SELECT 1 FROM live_status_minute_table lsm
                        WHERE lsm.live_id = ls.live_id AND lsm.live_action = '开始直播'
                    ) as is_full
                FROM live_session_table ls
                WHERE ls.live_id = $1
                LIMIT 1
            """

            session_data = await conn.fetchrow(optimized_query, live_id)

            if not session_data:
                logger.info(f"Live session {live_id} not found")
                return None

            # Build live info from single query result
            live_info = {
                "liveId": live_id,
                "isFinish": session_data["is_finish"],
                "isFull": session_data["is_full"],
                "parentArea": session_data["parent_area"] or "",
                "area": session_data["area"] or "",
                "coverUrl": session_data["cover"] or "",
                "danmakusCount": session_data["danmu_count"] or 0,
                "startDate": session_data["start_time_str"] or "",
                "stopDate": session_data["end_time_str"] or "",
                "title": session_data["title"] or "",
                "totalIncome": float(session_data["income"]) if session_data["income"] else 0.0,
                "watchCount": session_data["watch_change_count"] or 0,
                "likeCount": session_data["like_info_update_count"] or 0,
                "payCount": session_data["pay_count"] or 0,
                "interactionCount": session_data["interaction_count"] or 0,
                "onlineRank": session_data["max_online_rank"] or 0,
                "aveOnlineRank": session_data["ave_online_rank"] or 0,
                "aveEnterRoom": session_data["ave_enter_room"] or 0,
            }

            return live_info

    except asyncpg.PostgresError as e:
        logger.error(f"Database operation failed for live_id {live_id}: {e}")
        return None
    except Exception as e:
        logger.error(f"查询完整直播信息时发生未知错误 for live_id {live_id}: {e}")
        return None


async def query_minutes_live_info_with_live_id_optimized(live_id: str, interval: int = 10):
    """
    OPTIMIZED: Get base info and minute data efficiently
    """
    # Get base live info using optimized function
    base_live_info = await query_whole_live_info_with_live_id_optimized(live_id)
    if not base_live_info:
        logger.error(f"无法获取直播ID {live_id} 的基本信息")
        return None

    result = base_live_info.copy()
    result.update({
        "danmuCountList": [], "activeWatcherList": [], "onlineRankCountList": [],
        "incomeCountList": [], "interactCountList": [], "enterCountList": [], "timestamp": []
    })

    try:
        async with get_connection() as conn:
            # OPTIMIZATION: Query minute data from separate tables with proper sampling
            # Note: Minute-level data is stored in separate tables, not in live_status_minute_table
            minute_data_query = """
                WITH base_timestamps AS (
                    SELECT DISTINCT timestamp
                    FROM live_status_minute_table
                    WHERE live_id = $1
                    ORDER BY timestamp
                ),
                sampled_timestamps AS (
                    SELECT
                        timestamp,
                        ROW_NUMBER() OVER (ORDER BY timestamp) as rn
                    FROM base_timestamps
                ),
                filtered_timestamps AS (
                    SELECT timestamp
                    FROM sampled_timestamps
                    WHERE (rn - 1) % $2 = 0
                )
                SELECT
                    ft.timestamp,
                    COALESCE(dmt.count, 0) as danmu_count,
                    COALESCE(awt.count, 0) as active_watcher_count,
                    COALESCE(ort.count, 0) as online_rank_count,
                    COALESCE(imt.income, 0.0) as income_count,
                    COALESCE(iwt.count, 0) as interact_count,
                    COALESCE(ert.count, 0) as enter_count
                FROM filtered_timestamps ft
                LEFT JOIN danmu_count_minute_table dmt ON ft.timestamp = dmt.timestamp AND dmt.live_id = $1
                LEFT JOIN active_watcher_count_minute_table awt ON ft.timestamp = awt.timestamp AND awt.live_id = $1
                LEFT JOIN online_rank_count_minute_table ort ON ft.timestamp = ort.timestamp AND ort.live_id = $1
                LEFT JOIN income_minute_table imt ON ft.timestamp = imt.timestamp AND imt.live_id = $1
                LEFT JOIN interact_word_count_minute_table iwt ON ft.timestamp = iwt.timestamp AND iwt.live_id = $1
                LEFT JOIN enter_room_count_minute_table ert ON ft.timestamp = ert.timestamp AND ert.live_id = $1
                ORDER BY ft.timestamp
            """

            minute_records = await conn.fetch(minute_data_query, live_id, interval)

            for record in minute_records:
                result["timestamp"].append(record["timestamp"])
                result["danmuCountList"].append(record["danmu_count"] or 0)
                result["activeWatcherList"].append(record["active_watcher_count"] or 0)
                result["onlineRankCountList"].append(record["online_rank_count"] or 0)
                result["incomeCountList"].append(float(record["income_count"]) if record["income_count"] else 0.0)
                result["interactCountList"].append(record["interact_count"] or 0)
                result["enterCountList"].append(record["enter_count"] or 0)

    except asyncpg.PostgresError as e:
        logger.error(f"查询分钟级数据失败 for live_id {live_id}: {e}")
    except Exception as e:
        logger.error(f"处理分钟级数据时发生未知错误 for live_id {live_id}: {e}")

    return result
